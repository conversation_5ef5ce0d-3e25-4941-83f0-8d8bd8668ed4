using Alpaca.Markets;
using AlpacaMomentumBot.Console.Extensions;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Manages 2×ATR trailing stop-loss orders for capital preservation
/// </summary>
public sealed class StopManager : IStopManager
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<StopManager> _logger;

    public StopManager(
        IAlpacaClientFactory clientFactory,
        IMarketDataService marketDataService,
        ILogger<StopManager> logger)
    {
        _clientFactory = clientFactory;
        _marketDataService = marketDataService;
        _logger = logger;
    }

    public async Task UpdateTrailingStopsAsync(CancellationToken cancellationToken = default)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Get all open positions
                var positions = await tradingClient.ListPositionsAsync(cancellationToken);
                var openPositions = positions.Where(p => p.Quantity != 0).ToList();

                if (!openPositions.Any())
                {
                    _logger.LogInformation("No open positions found for stop updates");
                    return null;
                }

                _logger.LogInformation("Updating trailing stops for {Count} open positions", openPositions.Count);

                // Update stops for each position
                foreach (var position in openPositions)
                {
                    await UpdateStopForPositionAsync(tradingClient, position, cancellationToken);
                }

                _logger.LogInformation("Completed trailing stop updates");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating trailing stops");
                return null;
            }
        }, "UpdateTrailingStops");
    }

    public async Task SetInitialStopAsync(string symbol, decimal entryPrice, decimal atr, decimal quantity, CancellationToken cancellationToken = default)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Calculate initial stop price: entry - 2×ATR
                var stopPrice = entryPrice - (2m * atr);

                // Create stop-loss order (convert decimal quantity to integer)
                var orderQuantity = (int)Math.Abs(quantity);
                var stopOrder = new NewOrderRequest(symbol, orderQuantity, OrderSide.Sell, OrderType.Stop, TimeInForce.Gtc)
                {
                    StopPrice = stopPrice
                };

                var order = await tradingClient.PostOrderAsync(stopOrder, cancellationToken);

                _logger.LogInformation("Set initial stop for {Symbol}: StopPrice={StopPrice:C}, OrderId={OrderId}",
                    symbol, stopPrice, order.OrderId);

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting initial stop for {Symbol}", symbol);
                return null;
            }
        }, $"SetInitialStop-{symbol}");
    }

    public async Task RemoveStopsAsync(string symbol, CancellationToken cancellationToken = default)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Get all open orders for this symbol
                var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
                {
                    OrderStatusFilter = OrderStatusFilter.Open,
                    LimitOrderNumber = 100
                }, cancellationToken);

                var stopOrders = openOrders
                    .Where(o => o.Symbol == symbol && o.OrderType == OrderType.Stop)
                    .ToList();

                foreach (var order in stopOrders)
                {
                    await tradingClient.CancelOrderAsync(order.OrderId, cancellationToken);
                    _logger.LogInformation("Cancelled stop order {OrderId} for {Symbol}", order.OrderId, symbol);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing stops for {Symbol}", symbol);
                return null;
            }
        }, $"RemoveStops-{symbol}");
    }

    private async Task UpdateStopForPositionAsync(IAlpacaTradingClient tradingClient, IPosition position, CancellationToken cancellationToken)
    {
        try
        {
            var symbol = position.Symbol;
            var quantity = position.Quantity;

            // Skip if no position
            if (quantity == 0)
                return;

            // Get current market data for ATR calculation
            var startDate = DateTime.UtcNow.AddDays(-30); // Get 30 days for ATR calculation
            var endDate = DateTime.UtcNow;

            var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var bars = response.Items.ToList();

            if (bars.Count < 14) // Need at least 14 bars for ATR
            {
                _logger.LogWarning("Insufficient data for ATR calculation for {Symbol}, skipping stop update", symbol);
                return;
            }

            var currentPrice = bars.Last().Close;
            var currentAtr = (decimal)bars.GetAtr14();

            // Calculate new trailing stop price: currentPrice - 2×ATR
            var newStopPrice = currentPrice - (2m * currentAtr);

            // Get existing stop orders for this symbol
            var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
            {
                OrderStatusFilter = OrderStatusFilter.Open,
                LimitOrderNumber = 100
            }, cancellationToken);

            var existingStops = openOrders
                .Where(o => o.Symbol == symbol && o.OrderType == OrderType.Stop)
                .ToList();

            // For long positions, only move stop up (trailing)
            // For short positions, only move stop down (trailing)
            var shouldUpdateStop = false;
            decimal? currentStopPrice = null;

            if (existingStops.Any())
            {
                currentStopPrice = existingStops.First().StopPrice;
                
                if (quantity > 0) // Long position
                {
                    shouldUpdateStop = newStopPrice > currentStopPrice;
                }
                else // Short position
                {
                    shouldUpdateStop = newStopPrice < currentStopPrice;
                }
            }
            else
            {
                // No existing stop, create one
                shouldUpdateStop = true;
            }

            if (shouldUpdateStop)
            {
                // Cancel existing stops
                foreach (var existingStop in existingStops)
                {
                    await tradingClient.CancelOrderAsync(existingStop.OrderId, cancellationToken);
                    _logger.LogInformation("Cancelled existing stop {OrderId} for {Symbol}", existingStop.OrderId, symbol);
                }

                // Create new trailing stop (convert decimal quantity to integer)
                var orderSide = quantity > 0 ? OrderSide.Sell : OrderSide.Buy;
                var orderQuantity = (int)Math.Abs(quantity);
                var stopOrder = new NewOrderRequest(symbol, orderQuantity, orderSide, OrderType.Stop, TimeInForce.Gtc)
                {
                    StopPrice = newStopPrice
                };

                var newOrder = await tradingClient.PostOrderAsync(stopOrder, cancellationToken);

                _logger.LogInformation("Updated trailing stop for {Symbol}: OldStop={OldStop:C}, NewStop={NewStop:C}, CurrentPrice={Price:C}, ATR={ATR:C}, OrderId={OrderId}",
                    symbol, currentStopPrice, newStopPrice, currentPrice, currentAtr, newOrder.OrderId);
            }
            else
            {
                _logger.LogDebug("Stop for {Symbol} not updated: CurrentStop={CurrentStop:C}, CalculatedStop={CalculatedStop:C}",
                    symbol, currentStopPrice, newStopPrice);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating stop for position {Symbol}", position.Symbol);
        }
    }
}
