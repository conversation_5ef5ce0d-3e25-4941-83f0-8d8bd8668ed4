using AlpacaMomentumBot.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Examples;

/// <summary>
/// Example demonstrating how to use the real-time streaming services
/// for both Alpaca (equities) and Polygon (indices/volatility)
/// </summary>
public class StreamingExample
{
    public static async Task Main(string[] args)
    {
        // Setup dependency injection and configuration
        var services = new ServiceCollection();
        
        // Configure logging
        services.AddLogging(builder => 
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });
        
        // Load configuration from environment variables and appsettings.json
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Register HTTP client factory
        services.AddHttpClient();
        
        // Register our services
        services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
        services.AddSingleton<IStreamingDataService, StreamingDataService>();
        
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<StreamingExample>>();
        var streamingService = serviceProvider.GetRequiredService<IStreamingDataService>();
        
        try
        {
            logger.LogInformation("Starting streaming example...");
            
            // Set up event handlers
            SetupEventHandlers(streamingService, logger);
            
            // Connect to both Alpaca and Polygon streams
            await ConnectToStreams(streamingService, logger);
            
            // Subscribe to equity data (Alpaca)
            await SubscribeToEquityData(streamingService, logger);
            
            // Subscribe to index/volatility data (Polygon)
            await SubscribeToIndexData(streamingService, logger);
            
            // Run for a specified duration
            logger.LogInformation("Streaming data for 30 seconds...");
            await Task.Delay(TimeSpan.FromSeconds(30));
            
            // Clean up
            await CleanupStreams(streamingService, logger);
            
            logger.LogInformation("Streaming example completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in streaming example");
        }
        finally
        {
            serviceProvider.Dispose();
        }
    }
    
    private static void SetupEventHandlers(IStreamingDataService streamingService, ILogger logger)
    {
        logger.LogInformation("Setting up event handlers...");
        
        // Handle real-time quotes from Alpaca
        streamingService.QuoteReceived += (sender, args) =>
        {
            logger.LogInformation("Quote: {Symbol} Bid: {Bid} Ask: {Ask} @ {Time}",
                args.Symbol, args.BidPrice, args.AskPrice, args.Timestamp);
        };
        
        // Handle real-time bars from Alpaca
        streamingService.BarReceived += (sender, args) =>
        {
            logger.LogInformation("Bar: {Symbol} O: {Open} H: {High} L: {Low} C: {Close} V: {Volume} @ {Time}",
                args.Symbol, args.Open, args.High, args.Low, args.Close, args.Volume, args.Timestamp);
        };
        
        // Handle trade updates from Alpaca
        streamingService.TradeUpdated += (sender, args) =>
        {
            logger.LogInformation("Trade Update: {Symbol} {Side} {Quantity} @ {Price} Status: {Status}",
                args.Symbol, args.Side, args.Quantity, args.Price, args.Status);
        };
        
        // Handle index updates from Polygon (VIX spikes, SPX movements)
        streamingService.IndexUpdated += (sender, args) =>
        {
            logger.LogInformation("Index: {Symbol} Value: {Value} Change: {Change} ({ChangePercent}%) @ {Time}",
                args.IndexSymbol, args.Value, args.Change, args.ChangePercent, args.Timestamp);
                
            // Example: Detect VIX spikes
            if (args.IndexSymbol == "I:VIX" && args.Value > 30)
            {
                logger.LogWarning("VIX SPIKE DETECTED: {Value} - High volatility!", args.Value);
            }
        };
    }
    
    private static async Task ConnectToStreams(IStreamingDataService streamingService, ILogger logger)
    {
        logger.LogInformation("Connecting to streaming services...");
        
        try
        {
            // Connect to Alpaca for equity streaming
            await streamingService.ConnectAlpacaStreamAsync();
            logger.LogInformation("Connected to Alpaca streaming");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to connect to Alpaca streaming - check API credentials");
        }
        
        try
        {
            // Connect to Polygon for index streaming
            await streamingService.ConnectPolygonStreamAsync();
            logger.LogInformation("Connected to Polygon streaming");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to connect to Polygon streaming - check API key");
        }
    }
    
    private static async Task SubscribeToEquityData(IStreamingDataService streamingService, ILogger logger)
    {
        logger.LogInformation("Subscribing to equity data...");
        
        try
        {
            // Subscribe to quotes for major stocks
            var equitySymbols = new[] { "AAPL", "MSFT", "GOOGL", "TSLA", "SPY" };
            await streamingService.SubscribeToQuotesAsync(equitySymbols);
            logger.LogInformation("Subscribed to quotes for: {Symbols}", string.Join(", ", equitySymbols));
            
            // Subscribe to minute bars
            await streamingService.SubscribeToBarsAsync(equitySymbols);
            logger.LogInformation("Subscribed to bars for: {Symbols}", string.Join(", ", equitySymbols));
            
            // Subscribe to trade updates (for your own trades)
            await streamingService.SubscribeToTradeUpdatesAsync();
            logger.LogInformation("Subscribed to trade updates");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to subscribe to equity data");
        }
    }
    
    private static async Task SubscribeToIndexData(IStreamingDataService streamingService, ILogger logger)
    {
        logger.LogInformation("Subscribing to index/volatility data...");
        
        try
        {
            // Subscribe to key indices and volatility measures
            var indexSymbols = new[] { "I:VIX", "I:SPX", "I:NDX", "I:RUT" };
            await streamingService.SubscribeToIndexUpdatesAsync(indexSymbols);
            logger.LogInformation("Subscribed to indices: {Symbols}", string.Join(", ", indexSymbols));
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to subscribe to index data");
        }
    }
    
    private static async Task CleanupStreams(IStreamingDataService streamingService, ILogger logger)
    {
        logger.LogInformation("Cleaning up streaming connections...");
        
        try
        {
            // Unsubscribe from all streams
            await streamingService.UnsubscribeAllAsync();
            logger.LogInformation("Unsubscribed from all streams");
            
            // Disconnect from all services
            await streamingService.DisconnectAllAsync();
            logger.LogInformation("Disconnected from all streaming services");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error during cleanup");
        }
    }
}

/// <summary>
/// Example configuration for the streaming services
/// Create this as appsettings.json in your project
/// </summary>
public static class ExampleConfiguration
{
    public const string AppSettingsJson = @"{
  ""Logging"": {
    ""LogLevel"": {
      ""Default"": ""Information"",
      ""Microsoft"": ""Warning"",
      ""Microsoft.Hosting.Lifetime"": ""Information""
    }
  },
  ""APCA_API_KEY_ID"": ""your-alpaca-key-id"",
  ""APCA_API_SECRET_KEY"": ""your-alpaca-secret-key"",
  ""APCA_API_ENV"": ""paper"",
  ""POLY_API_KEY"": ""your-polygon-api-key""
}";
}

/// <summary>
/// Example of using Polygon WebSocket client directly
/// </summary>
public class PolygonStreamingExample
{
    public static async Task RunPolygonExample()
    {
        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();
        
        var logger = LoggerFactory.Create(builder => builder.AddConsole())
            .CreateLogger<PolygonWebSocketClient>();
        
        using var client = new PolygonWebSocketClient(configuration, logger);
        
        // Set up event handlers
        client.ConnectionStatusChanged += (sender, args) =>
        {
            Console.WriteLine($"Polygon connection status: {args.Status} - {args.Message}");
        };
        
        client.IndexUpdated += (sender, args) =>
        {
            Console.WriteLine($"Index update: {args.IndexSymbol} = {args.Value} ({args.ChangePercent:F2}%)");
        };
        
        client.ErrorOccurred += (sender, args) =>
        {
            Console.WriteLine($"Polygon error: {args.Message}");
        };
        
        try
        {
            // Connect
            await client.ConnectAsync();
            
            // Wait for authentication
            await Task.Delay(2000);
            
            // Subscribe to VIX and SPX
            await client.SubscribeToIndexUpdatesAsync(new[] { "I:VIX", "I:SPX" });
            
            // Stream for 30 seconds
            await Task.Delay(30000);
            
            // Cleanup
            await client.UnsubscribeAllAsync();
            await client.DisconnectAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
