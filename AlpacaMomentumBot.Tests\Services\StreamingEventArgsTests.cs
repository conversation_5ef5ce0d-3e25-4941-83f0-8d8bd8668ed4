using AlpacaMomentumBot.Services;
using FluentAssertions;
using Xunit;

namespace AlpacaMomentumBot.Tests.Services;

public class StreamingEventArgsTests
{
    [Fact]
    public void StreamingQuoteEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;

        // Act
        var args = new StreamingQuoteEventArgs
        {
            Symbol = "AAPL",
            BidPrice = 150.25m,
            AskPrice = 150.30m,
            BidSize = 100,
            AskSize = 200,
            Timestamp = timestamp
        };

        // Assert
        args.Symbol.Should().Be("AAPL");
        args.BidPrice.Should().Be(150.25m);
        args.AskPrice.Should().Be(150.30m);
        args.BidSize.Should().Be(100);
        args.AskSize.Should().Be(200);
        args.Timestamp.Should().Be(timestamp);
    }

    [Fact]
    public void StreamingQuoteEventArgs_DefaultValues_ShouldBeCorrect()
    {
        // Act
        var args = new StreamingQuoteEventArgs();

        // Assert
        args.Symbol.Should().Be("");
        args.BidPrice.Should().Be(0m);
        args.AskPrice.Should().Be(0m);
        args.BidSize.Should().Be(0);
        args.AskSize.Should().Be(0);
        args.Timestamp.Should().Be(default(DateTime));
    }

    [Fact]
    public void StreamingBarEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;

        // Act
        var args = new StreamingBarEventArgs
        {
            Symbol = "MSFT",
            Open = 300.00m,
            High = 305.50m,
            Low = 299.75m,
            Close = 304.25m,
            Volume = 1000000,
            Timestamp = timestamp
        };

        // Assert
        args.Symbol.Should().Be("MSFT");
        args.Open.Should().Be(300.00m);
        args.High.Should().Be(305.50m);
        args.Low.Should().Be(299.75m);
        args.Close.Should().Be(304.25m);
        args.Volume.Should().Be(1000000);
        args.Timestamp.Should().Be(timestamp);
    }

    [Fact]
    public void StreamingBarEventArgs_DefaultValues_ShouldBeCorrect()
    {
        // Act
        var args = new StreamingBarEventArgs();

        // Assert
        args.Symbol.Should().Be("");
        args.Open.Should().Be(0m);
        args.High.Should().Be(0m);
        args.Low.Should().Be(0m);
        args.Close.Should().Be(0m);
        args.Volume.Should().Be(0);
        args.Timestamp.Should().Be(default(DateTime));
    }

    [Fact]
    public void IndexUpdateEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;

        // Act
        var args = new IndexUpdateEventArgs
        {
            IndexSymbol = "I:VIX",
            Value = 25.50m,
            Change = 1.25m,
            ChangePercent = 5.15m,
            Timestamp = timestamp
        };

        // Assert
        args.IndexSymbol.Should().Be("I:VIX");
        args.Value.Should().Be(25.50m);
        args.Change.Should().Be(1.25m);
        args.ChangePercent.Should().Be(5.15m);
        args.Timestamp.Should().Be(timestamp);
    }

    [Fact]
    public void IndexUpdateEventArgs_DefaultValues_ShouldBeCorrect()
    {
        // Act
        var args = new IndexUpdateEventArgs();

        // Assert
        args.IndexSymbol.Should().Be("");
        args.Value.Should().Be(0m);
        args.Change.Should().Be(0m);
        args.ChangePercent.Should().Be(0m);
        args.Timestamp.Should().Be(default(DateTime));
    }

    [Fact]
    public void TradeUpdateEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;

        // Act
        var args = new TradeUpdateEventArgs
        {
            Symbol = "TSLA",
            OrderId = "12345",
            Quantity = 100m,
            Price = 250.75m,
            Side = "buy",
            Status = "filled",
            Timestamp = timestamp
        };

        // Assert
        args.Symbol.Should().Be("TSLA");
        args.OrderId.Should().Be("12345");
        args.Quantity.Should().Be(100m);
        args.Price.Should().Be(250.75m);
        args.Side.Should().Be("buy");
        args.Status.Should().Be("filled");
        args.Timestamp.Should().Be(timestamp);
    }

    [Fact]
    public void TradeUpdateEventArgs_DefaultValues_ShouldBeCorrect()
    {
        // Act
        var args = new TradeUpdateEventArgs();

        // Assert
        args.Symbol.Should().Be("");
        args.OrderId.Should().Be("");
        args.Quantity.Should().Be(0m);
        args.Price.Should().Be(0m);
        args.Side.Should().Be("");
        args.Status.Should().Be("");
        args.Timestamp.Should().Be(default(DateTime));
    }
}

public class StreamingConnectionStatusTests
{
    [Fact]
    public void StreamingConnectionStatus_ShouldHaveCorrectValues()
    {
        // Assert
        StreamingConnectionStatus.Disconnected.Should().Be(StreamingConnectionStatus.Disconnected);
        StreamingConnectionStatus.Connecting.Should().Be(StreamingConnectionStatus.Connecting);
        StreamingConnectionStatus.Connected.Should().Be(StreamingConnectionStatus.Connected);
        StreamingConnectionStatus.Reconnecting.Should().Be(StreamingConnectionStatus.Reconnecting);
        StreamingConnectionStatus.Error.Should().Be(StreamingConnectionStatus.Error);
    }

    [Fact]
    public void StreamingConnectionStatus_ShouldBeEnumerable()
    {
        // Act
        var values = Enum.GetValues<StreamingConnectionStatus>();

        // Assert
        values.Should().Contain(StreamingConnectionStatus.Disconnected);
        values.Should().Contain(StreamingConnectionStatus.Connecting);
        values.Should().Contain(StreamingConnectionStatus.Connected);
        values.Should().Contain(StreamingConnectionStatus.Reconnecting);
        values.Should().Contain(StreamingConnectionStatus.Error);
        values.Should().HaveCount(5);
    }
}

public class PolygonConnectionStatusTests
{
    [Fact]
    public void PolygonConnectionStatus_ShouldHaveCorrectValues()
    {
        // Assert
        PolygonConnectionStatus.Disconnected.Should().Be(PolygonConnectionStatus.Disconnected);
        PolygonConnectionStatus.Connecting.Should().Be(PolygonConnectionStatus.Connecting);
        PolygonConnectionStatus.Connected.Should().Be(PolygonConnectionStatus.Connected);
        PolygonConnectionStatus.Authenticating.Should().Be(PolygonConnectionStatus.Authenticating);
        PolygonConnectionStatus.Authenticated.Should().Be(PolygonConnectionStatus.Authenticated);
        PolygonConnectionStatus.Reconnecting.Should().Be(PolygonConnectionStatus.Reconnecting);
        PolygonConnectionStatus.Error.Should().Be(PolygonConnectionStatus.Error);
    }

    [Fact]
    public void PolygonConnectionStatus_ShouldBeEnumerable()
    {
        // Act
        var values = Enum.GetValues<PolygonConnectionStatus>();

        // Assert
        values.Should().Contain(PolygonConnectionStatus.Disconnected);
        values.Should().Contain(PolygonConnectionStatus.Connecting);
        values.Should().Contain(PolygonConnectionStatus.Connected);
        values.Should().Contain(PolygonConnectionStatus.Authenticating);
        values.Should().Contain(PolygonConnectionStatus.Authenticated);
        values.Should().Contain(PolygonConnectionStatus.Reconnecting);
        values.Should().Contain(PolygonConnectionStatus.Error);
        values.Should().HaveCount(7);
    }
}

public class EventArgsInheritanceTests
{
    [Fact]
    public void StreamingQuoteEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new StreamingQuoteEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }

    [Fact]
    public void StreamingBarEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new StreamingBarEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }

    [Fact]
    public void IndexUpdateEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new IndexUpdateEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }

    [Fact]
    public void TradeUpdateEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new TradeUpdateEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }

    [Fact]
    public void PolygonConnectionStatusEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new PolygonConnectionStatusEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }

    [Fact]
    public void PolygonIndexUpdateEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new PolygonIndexUpdateEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }

    [Fact]
    public void PolygonErrorEventArgs_ShouldInheritFromEventArgs()
    {
        // Act
        var args = new PolygonErrorEventArgs();

        // Assert
        args.Should().BeAssignableTo<EventArgs>();
    }
}
