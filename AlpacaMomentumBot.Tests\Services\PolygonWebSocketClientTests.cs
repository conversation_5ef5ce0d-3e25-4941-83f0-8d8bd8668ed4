using AlpacaMomentumBot.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AlpacaMomentumBot.Tests.Services;

public class PolygonWebSocketClientTests
{
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<PolygonWebSocketClient>> _mockLogger;
    private readonly PolygonWebSocketClient _client;

    public PolygonWebSocketClientTests()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<PolygonWebSocketClient>>();
        
        // Setup configuration with API key
        _mockConfiguration.Setup(c => c["POLY_API_KEY"]).Returns("test-api-key");
        
        _client = new PolygonWebSocketClient(_mockConfiguration.Object, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_ShouldInitializeWithDisconnectedStatus()
    {
        // Assert
        _client.ConnectionStatus.Should().Be(PolygonConnectionStatus.Disconnected);
    }

    [Fact]
    public void Constructor_WithoutApiKey_ShouldStillInitialize()
    {
        // Arrange
        var mockConfig = new Mock<IConfiguration>();
        mockConfig.Setup(c => c["POLY_API_KEY"]).Returns((string?)null);

        // Act
        var client = new PolygonWebSocketClient(mockConfig.Object, _mockLogger.Object);

        // Assert
        client.ConnectionStatus.Should().Be(PolygonConnectionStatus.Disconnected);
    }

    [Fact]
    public async Task ConnectAsync_WithoutApiKey_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var mockConfig = new Mock<IConfiguration>();
        mockConfig.Setup(c => c["POLY_API_KEY"]).Returns((string?)null);
        var client = new PolygonWebSocketClient(mockConfig.Object, _mockLogger.Object);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => client.ConnectAsync());
    }

    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_WhenNotAuthenticated_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _client.SubscribeToIndexUpdatesAsync(symbols));
    }

    [Fact]
    public async Task UnsubscribeFromIndexUpdatesAsync_WhenNotAuthenticated_ShouldNotThrow()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };

        // Act & Assert
        await _client.UnsubscribeFromIndexUpdatesAsync(symbols); // Should not throw
    }

    [Fact]
    public async Task UnsubscribeAllAsync_WhenNoSubscriptions_ShouldNotThrow()
    {
        // Act & Assert
        await _client.UnsubscribeAllAsync(); // Should not throw
    }

    [Fact]
    public async Task DisconnectAsync_WhenAlreadyDisconnected_ShouldNotThrow()
    {
        // Act & Assert
        await _client.DisconnectAsync(); // Should not throw
    }

    [Fact]
    public void ConnectionStatusChanged_Event_ShouldBeRaised()
    {
        // Arrange
        PolygonConnectionStatusEventArgs? receivedArgs = null;
        _client.ConnectionStatusChanged += (sender, args) => receivedArgs = args;

        // Act - This would normally be triggered by internal status changes
        // For testing, we'll verify the event can be subscribed to
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void IndexUpdated_Event_ShouldBeRaised()
    {
        // Arrange
        PolygonIndexUpdateEventArgs? receivedArgs = null;
        _client.IndexUpdated += (sender, args) => receivedArgs = args;

        // Act - This would normally be triggered by incoming WebSocket messages
        // For testing, we'll verify the event can be subscribed to
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void ErrorOccurred_Event_ShouldBeRaised()
    {
        // Arrange
        PolygonErrorEventArgs? receivedArgs = null;
        _client.ErrorOccurred += (sender, args) => receivedArgs = args;

        // Act - This would normally be triggered by errors
        // For testing, we'll verify the event can be subscribed to
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        _client.Dispose(); // Should not throw
    }

    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert
        _client.Dispose();
        _client.Dispose(); // Should not throw on second call
    }
}

public class PolygonMessageTypesTests
{
    [Fact]
    public void MessageTypes_ShouldHaveCorrectValues()
    {
        // Assert
        PolygonMessageTypes.Status.Should().Be("status");
        PolygonMessageTypes.IndexValue.Should().Be("V");
        PolygonMessageTypes.Error.Should().Be("error");
        PolygonMessageTypes.Success.Should().Be("success");
    }
}

public class PolygonWebSocketMessageTests
{
    [Fact]
    public void PolygonStatusMessage_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var message = new PolygonStatusMessage
        {
            EventType = "status",
            Status = "connected",
            Message = "Successfully connected"
        };

        // Assert
        message.EventType.Should().Be("status");
        message.Status.Should().Be("connected");
        message.Message.Should().Be("Successfully connected");
    }

    [Fact]
    public void PolygonIndexValueMessage_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var message = new PolygonIndexValueMessage
        {
            EventType = "V",
            Symbol = "I:VIX",
            Value = 25.50m,
            Timestamp = 1640995200000, // 2022-01-01 00:00:00 UTC
            Change = 1.25m,
            ChangePercent = 5.15m,
            Volume = 1000
        };

        // Assert
        message.EventType.Should().Be("V");
        message.Symbol.Should().Be("I:VIX");
        message.Value.Should().Be(25.50m);
        message.Timestamp.Should().Be(1640995200000);
        message.Change.Should().Be(1.25m);
        message.ChangePercent.Should().Be(5.15m);
        message.Volume.Should().Be(1000);
    }

    [Fact]
    public void PolygonErrorMessage_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var message = new PolygonErrorMessage
        {
            EventType = "error",
            Message = "Authentication failed",
            Code = 401
        };

        // Assert
        message.EventType.Should().Be("error");
        message.Message.Should().Be("Authentication failed");
        message.Code.Should().Be(401);
    }
}

public class PolygonEventArgsTests
{
    [Fact]
    public void PolygonConnectionStatusEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var exception = new Exception("Test exception");

        // Act
        var args = new PolygonConnectionStatusEventArgs
        {
            Status = PolygonConnectionStatus.Connected,
            Message = "Connected successfully",
            Exception = exception
        };

        // Assert
        args.Status.Should().Be(PolygonConnectionStatus.Connected);
        args.Message.Should().Be("Connected successfully");
        args.Exception.Should().Be(exception);
    }

    [Fact]
    public void PolygonIndexUpdateEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;

        // Act
        var args = new PolygonIndexUpdateEventArgs
        {
            IndexSymbol = "I:VIX",
            Value = 25.50m,
            Change = 1.25m,
            ChangePercent = 5.15m,
            Timestamp = timestamp,
            Volume = 1000
        };

        // Assert
        args.IndexSymbol.Should().Be("I:VIX");
        args.Value.Should().Be(25.50m);
        args.Change.Should().Be(1.25m);
        args.ChangePercent.Should().Be(5.15m);
        args.Timestamp.Should().Be(timestamp);
        args.Volume.Should().Be(1000);
    }

    [Fact]
    public void PolygonErrorEventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var exception = new Exception("Test exception");

        // Act
        var args = new PolygonErrorEventArgs
        {
            Message = "Error occurred",
            ErrorCode = 500,
            Exception = exception
        };

        // Assert
        args.Message.Should().Be("Error occurred");
        args.ErrorCode.Should().Be(500);
        args.Exception.Should().Be(exception);
    }
}
