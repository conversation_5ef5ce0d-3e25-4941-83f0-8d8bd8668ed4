using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AlpacaMomentumBot.Models;

/// <summary>
/// Entity representing a cached index bar in SQLite database.
/// Stores historical index data to avoid re-downloading unchanged history from Polygon API.
/// </summary>
[Table("CachedIndexBars")]
[Index(nameof(Symbol), nameof(TimeUtc), IsUnique = true)]
public class CachedIndexBar
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Index symbol (e.g., "I:SPX", "I:VIX")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Bar timestamp in UTC (converted from Polygon's milliseconds since epoch)
    /// </summary>
    [Required]
    public DateTime TimeUtc { get; set; }

    /// <summary>
    /// Opening price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Open { get; set; }

    /// <summary>
    /// Highest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal High { get; set; }

    /// <summary>
    /// Lowest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Low { get; set; }

    /// <summary>
    /// Closing price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }

    /// <summary>
    /// Trading volume for the time period
    /// </summary>
    [Required]
    public long Volume { get; set; }

    /// <summary>
    /// When this record was cached (for cache management)
    /// </summary>
    [Required]
    public DateTime CachedAt { get; set; }

    /// <summary>
    /// Converts this cached entity to an IndexBar record
    /// </summary>
    public Services.IndexBar ToIndexBar()
    {
        return new Services.IndexBar(TimeUtc, Open, High, Low, Close, Volume);
    }

    /// <summary>
    /// Creates a CachedIndexBar from an IndexBar and symbol
    /// </summary>
    public static CachedIndexBar FromIndexBar(string symbol, Services.IndexBar indexBar)
    {
        return new CachedIndexBar
        {
            Symbol = symbol,
            TimeUtc = indexBar.TimeUtc,
            Open = indexBar.Open,
            High = indexBar.High,
            Low = indexBar.Low,
            Close = indexBar.Close,
            Volume = indexBar.Volume,
            CachedAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Metadata about cached data for each symbol to track cache freshness
/// </summary>
[Table("CacheMetadata")]
public class CacheMetadata
{
    [Key]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Latest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime LatestDataDate { get; set; }

    /// <summary>
    /// When the cache was last updated
    /// </summary>
    [Required]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Total number of cached bars for this symbol
    /// </summary>
    [Required]
    public int BarCount { get; set; }
}
