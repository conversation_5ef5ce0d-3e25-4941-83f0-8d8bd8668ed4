using Microsoft.EntityFrameworkCore;
using AlpacaMomentumBot.Models;

namespace AlpacaMomentumBot.Data;

/// <summary>
/// Entity Framework DbContext for SQLite index bar caching.
/// Manages cached index data to reduce API calls to Polygon.
/// </summary>
public class IndexCacheDbContext : DbContext
{
    public IndexCacheDbContext(DbContextOptions<IndexCacheDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Cached index bars from Polygon API
    /// </summary>
    public DbSet<CachedIndexBar> CachedIndexBars { get; set; } = null!;

    /// <summary>
    /// Metadata about cached data for each symbol
    /// </summary>
    public DbSet<CacheMetadata> CacheMetadata { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CachedIndexBar
        modelBuilder.Entity<CachedIndexBar>(entity =>
        {
            // Composite index for efficient queries
            entity.HasIndex(e => new { e.Symbol, e.TimeUtc })
                  .IsUnique()
                  .HasDatabaseName("IX_CachedIndexBars_Symbol_TimeUtc");

            // Additional index for date range queries
            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_CachedIndexBars_TimeUtc");

            // Symbol index for metadata queries
            entity.HasIndex(e => e.Symbol)
                  .HasDatabaseName("IX_CachedIndexBars_Symbol");
        });

        // Configure CacheMetadata
        modelBuilder.Entity<CacheMetadata>(entity =>
        {
            entity.HasKey(e => e.Symbol);
        });
    }

    /// <summary>
    /// Ensures the database is created and migrations are applied
    /// </summary>
    public async Task EnsureDatabaseCreatedAsync()
    {
        await Database.EnsureCreatedAsync();
    }

    /// <summary>
    /// Gets cached bars for a symbol within a date range
    /// </summary>
    public async Task<List<CachedIndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        return await CachedIndexBars
            .Where(b => b.Symbol == symbol && b.TimeUtc >= startDate && b.TimeUtc <= endDate)
            .OrderBy(b => b.TimeUtc)
            .ToListAsync();
    }

    /// <summary>
    /// Gets the latest cached date for a symbol
    /// </summary>
    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol)
    {
        var metadata = await CacheMetadata.FindAsync(symbol);
        return metadata?.LatestDataDate;
    }

    /// <summary>
    /// Adds or updates cached bars for a symbol
    /// </summary>
    public async Task AddOrUpdateCachedBarsAsync(string symbol, IEnumerable<Services.IndexBar> indexBars)
    {
        var cachedBars = indexBars.Select(bar => CachedIndexBar.FromIndexBar(symbol, bar)).ToList();
        
        if (!cachedBars.Any())
            return;

        // Add new bars (EF will handle duplicates based on unique constraint)
        foreach (var bar in cachedBars)
        {
            var existing = await CachedIndexBars
                .FirstOrDefaultAsync(b => b.Symbol == symbol && b.TimeUtc == bar.TimeUtc);
            
            if (existing == null)
            {
                CachedIndexBars.Add(bar);
            }
        }

        // Update metadata
        var latestDate = cachedBars.Max(b => b.TimeUtc);
        var metadata = await CacheMetadata.FindAsync(symbol);
        
        if (metadata == null)
        {
            metadata = new CacheMetadata
            {
                Symbol = symbol,
                LatestDataDate = latestDate,
                LastUpdated = DateTime.UtcNow,
                BarCount = cachedBars.Count
            };
            CacheMetadata.Add(metadata);
        }
        else
        {
            if (latestDate > metadata.LatestDataDate)
            {
                metadata.LatestDataDate = latestDate;
            }
            metadata.LastUpdated = DateTime.UtcNow;
            metadata.BarCount = await CachedIndexBars.CountAsync(b => b.Symbol == symbol);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Cleans up old cached data (older than specified days)
    /// </summary>
    public async Task CleanupOldDataAsync(int retainDays = 365)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
        
        var oldBars = CachedIndexBars.Where(b => b.TimeUtc < cutoffDate);
        CachedIndexBars.RemoveRange(oldBars);
        
        await SaveChangesAsync();
    }
}
