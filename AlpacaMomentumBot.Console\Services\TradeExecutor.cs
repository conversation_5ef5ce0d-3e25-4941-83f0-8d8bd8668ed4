using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

public sealed class TradeExecutor : ITradeExecutor
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IStopManager _stopManager;
    private readonly ILogger<TradeExecutor> _logger;

    public TradeExecutor(IAlpacaClientFactory clientFactory, IStopManager stopManager, ILogger<TradeExecutor> logger)
    {
        _clientFactory = clientFactory;
        _stopManager = stopManager;
        _logger = logger;
    }

    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        if (quantity <= 0)
        {
            _logger.LogWarning("Invalid quantity {Quantity} for {Symbol}, skipping trade", quantity, signal.Symbol);
            return;
        }

        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Cancel existing orders for this symbol
                await CancelExistingOrdersAsync(tradingClient, signal.Symbol);

                // Calculate entry price: lastClose * 1.002m (Limit-on-Open)
                var entryPrice = signal.Price * 1.002m;

                // Calculate stop-loss price: entry - 2×ATR
                var stopLossPrice = entryPrice - (2m * signal.Atr);

                // Log the trade execution (placeholder for actual order submission)
                _logger.LogInformation("Would execute trade for {Symbol}: Quantity={Quantity}, EntryPrice={EntryPrice:C}, StopPrice={StopPrice:C}",
                    signal.Symbol, quantity, entryPrice, stopLossPrice);

                // Set initial trailing stop using StopManager
                await _stopManager.SetInitialStopAsync(signal.Symbol, entryPrice, signal.Atr, quantity);

                // TODO: Implement actual order submission once API usage is clarified
                // When actual orders are implemented, the stop should be set after the entry order is filled
                return (object)null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                return (object)null;
            }
        }, $"ExecuteTrade-{signal.Symbol}");
    }

    private async Task CancelExistingOrdersAsync(IAlpacaTradingClient tradingClient, string symbol)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
                {
                    OrderStatusFilter = OrderStatusFilter.Open,
                    LimitOrderNumber = 100
                });

                var symbolOrders = openOrders.Where(o => o.Symbol == symbol).ToList();

                foreach (var order in symbolOrders)
                {
                    await tradingClient.CancelOrderAsync(order.OrderId);
                    _logger.LogInformation("Cancelled existing order {OrderId} for {Symbol}", order.OrderId, symbol);
                }
                return (object)null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error cancelling existing orders for {Symbol}", symbol);
                return (object)null;
            }
        }, $"CancelOrders-{symbol}");
    }
}
