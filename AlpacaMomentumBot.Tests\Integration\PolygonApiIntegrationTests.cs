using AlpacaMomentumBot.Services;
using AlpacaMomentumBot.Data;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;
using DotNetEnv;

namespace AlpacaMomentumBot.Tests.Integration;

public class PolygonApiIntegrationTests : IDisposable
{
    private readonly IHost _host;
    private readonly IMarketDataService _marketDataService;

    public PolygonApiIntegrationTests()
    {
        // Load environment variables for integration testing
        Env.Load("AlpacaMomentumBot.Console/.env");

        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                // HTTP client factory for Polygon
                services.AddHttpClient();

                // Rate limiting policies
                services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

                // Configure Polygon HTTP client with Polly policies
                services.AddHttpClient("polygon", client =>
                {
                    client.BaseAddress = new Uri("https://api.polygon.io/");
                    client.Timeout = TimeSpan.FromSeconds(30);
                })
                .AddPolicyHandler((serviceProvider, request) =>
                {
                    var policyFactory = serviceProvider.GetRequiredService<IRateLimitPolicyFactory>();
                    return policyFactory.CreatePolygonPolicy();
                });

                // Database and caching for testing
                services.AddDbContext<IndexCacheDbContext>(options =>
                    options.UseInMemoryDatabase("TestIndexCache"));
                services.AddScoped<IIndexCacheService, IndexCacheService>();

                // Mock Alpaca factory for this test
                var mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
                services.AddSingleton(mockAlpacaFactory.Object);

                // Real Polygon factory
                services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
                services.AddSingleton<IMarketDataService, MarketDataService>();
            })
            .Build();

        _marketDataService = _host.Services.GetRequiredService<IMarketDataService>();
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task GetIndexValueAsync_WithValidIndex_ShouldReturnValue()
    {
        // Skip test if no API key is configured
        var config = _host.Services.GetRequiredService<IConfiguration>();
        var apiKey = config["POLY_API_KEY"];
        
        if (string.IsNullOrEmpty(apiKey) || apiKey == "REPLACE_ME")
        {
            // Skip test if no real API key is configured
            return;
        }

        // Arrange
        var indexSymbol = "I:SPX"; // S&P 500 index

        // Act
        var result = await _marketDataService.GetIndexValueAsync(indexSymbol);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task GetIndexBarsAsync_WithValidIndex_ShouldReturnBars()
    {
        // Skip test if no API key is configured
        var config = _host.Services.GetRequiredService<IConfiguration>();
        var apiKey = config["POLY_API_KEY"];
        
        if (string.IsNullOrEmpty(apiKey) || apiKey == "REPLACE_ME")
        {
            // Skip test if no real API key is configured
            return;
        }

        // Arrange
        var indexSymbol = "I:SPX"; // S&P 500 index
        var startDate = DateTime.UtcNow.AddDays(-10);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // Act
        var result = await _marketDataService.GetIndexBarsAsync(indexSymbol, startDate, endDate);

        // Assert
        result.Should().NotBeNull();
        result.Should().NotBeEmpty();
        
        // Verify the bars have valid data
        foreach (var bar in result)
        {
            bar.Open.Should().BeGreaterThan(0);
            bar.High.Should().BeGreaterThan(0);
            bar.Low.Should().BeGreaterThan(0);
            bar.Close.Should().BeGreaterThan(0);
            bar.High.Should().BeGreaterOrEqualTo(bar.Low);
        }
    }

    public void Dispose()
    {
        _host?.Dispose();
    }
}
